# 邻里办小程序后台管理系统

## 项目简介

邻里办小程序后台管理系统是一个基于 Vue.js 2.x 开发的现代化管理平台，专为社区服务和邻里互助小程序提供后台管理支持。系统集成了用户管理、商城管理、房屋管理、工作流引擎、系统监控等多个功能模块，为社区运营提供全方位的管理解决方案。

## 技术栈

### 前端技术
- **Vue.js 2.6.12** - 渐进式 JavaScript 框架
- **Element UI 2.15.8** - 基于 Vue 2.0 的桌面端组件库
- **Vue Router 3.4.9** - Vue.js 官方路由管理器
- **Vuex 3.6.0** - Vue.js 应用程序的状态管理模式
- **Axios 0.24.0** - 基于 Promise 的 HTTP 库

### 工作流引擎
- **BPMN.js 7.4.0** - 流程建模工具
- **Flowable** - 轻量级业务流程引擎

### 开发工具
- **Vue CLI 4.4.6** - Vue.js 开发的标准工具
- **Webpack** - 模块打包器
- **Sass** - CSS 预处理器
- **ESLint** - JavaScript 代码检查工具

### 其他依赖
- **ECharts 4.9.0** - 数据可视化图表库
- **Quill 1.3.7** - 富文本编辑器
- **Three.js 0.121.0** - 3D 图形库
- **JSEncrypt** - RSA 加密库

## 主要功能模块

### 🏠 房屋管理
- **房源小区管理** - 小区信息维护、地理位置管理
- **房源详情管理** - 房源信息录入、审核、上下架管理
- **房源状态控制** - 出租状态、审核流程、用户授权

### 🛒 商城管理
- **商品管理** - 商品信息、分类、规格、库存管理
- **订单管理** - 订单处理、状态跟踪、售后服务
- **分类管理** - 商品分类树形结构管理
- **广告管理** - 轮播图、推广位管理
- **收货地址** - 用户地址信息管理

### 👥 系统管理
- **用户管理** - 用户信息、角色分配、权限控制
- **角色管理** - 角色定义、权限分配
- **菜单管理** - 系统菜单、权限配置
- **部门管理** - 组织架构管理
- **字典管理** - 系统字典数据维护
- **参数配置** - 系统参数设置

### 🔄 工作流引擎
- **流程设计** - 可视化流程建模工具
- **流程部署** - 流程定义部署管理
- **任务管理** - 待办任务、已办任务、我的发起
- **流程监控** - 流程实例跟踪、流转记录

### 📊 系统监控
- **在线用户** - 在线用户监控、强制下线
- **登录日志** - 用户登录记录、安全审计
- **操作日志** - 系统操作记录、行为追踪
- **定时任务** - 任务调度、执行监控
- **服务监控** - 系统性能、资源使用情况
- **缓存监控** - Redis 缓存状态监控

### 🛠️ 开发工具
- **代码生成** - 基于数据库表自动生成 CRUD 代码
- **表单构建** - 可视化表单设计器
- **系统接口** - Swagger API 文档

### 📝 内容管理
- **文章管理** - 内容发布、编辑、分类管理
- **通知公告** - 系统通知、公告发布
- **消息管理** - 站内消息、推送管理
- **意见反馈** - 用户反馈收集、处理

## 项目特色

### 🎨 现代化 UI 设计
- 基于 Element UI 的现代化界面设计
- 响应式布局，支持多种屏幕尺寸
- 丰富的图表展示，基于 ECharts 实现数据可视化
- 支持主题切换和个性化配置

### 🔐 完善的权限体系
- 基于 RBAC 的权限控制模型
- 细粒度的菜单和按钮权限控制
- JWT Token 认证机制
- 支持多角色、多部门权限管理

### 🔄 强大的工作流引擎
- 集成 Flowable 工作流引擎
- 可视化流程设计器
- 支持复杂业务流程建模
- 完整的任务生命周期管理

### 📱 小程序后台支持
- 专为小程序设计的后台管理
- 微信 API 集成支持
- 小程序用户管理
- 数据统计和分析

## 快速开始

### 环境要求
- Node.js >= 8.9
- npm >= 3.0.0

### 安装依赖

```bash
# 克隆项目
git clone https://gitee.com/open-source-byte/source-vue.git

# 进入项目目录
cd source-vue

# 安装依赖
npm install

# 建议不要直接使用 cnpm 安装依赖，会有各种诡异的 bug
# 可以通过如下操作解决 npm 下载速度慢的问题
npm install --registry=https://registry.npm.taobao.org
```

### 开发环境启动

```bash
# 启动开发服务器
npm run dev
```

浏览器访问 http://localhost:8080

### 生产环境构建

```bash
# 构建生产版本
npm run build

# 预览构建结果
npm run preview
```

## 项目结构

```
src/
├── api/                    # API 接口
│   ├── flowable/          # 工作流相关接口
│   ├── house/             # 房屋管理接口
│   ├── mall/              # 商城管理接口
│   ├── monitor/           # 系统监控接口
│   ├── system/            # 系统管理接口
│   └── tool/              # 开发工具接口
├── assets/                # 静态资源
│   ├── icons/             # SVG 图标
│   ├── images/            # 图片资源
│   └── styles/            # 样式文件
├── components/            # 公共组件
│   ├── ProcessDesigner/   # 流程设计器
│   ├── ProcessViewer/     # 流程查看器
│   └── ...                # 其他组件
├── directive/             # 自定义指令
├── layout/                # 布局组件
├── plugins/               # 插件配置
├── router/                # 路由配置
├── store/                 # Vuex 状态管理
├── utils/                 # 工具函数
├── views/                 # 页面组件
│   ├── dashboard/         # 仪表盘
│   ├── flowable/          # 工作流管理
│   ├── house/             # 房屋管理
│   ├── mall/              # 商城管理
│   ├── monitor/           # 系统监控
│   ├── system/            # 系统管理
│   └── tool/              # 开发工具
└── main.js                # 应用入口
```

## 配置说明

### 环境配置
项目支持多环境配置，可在 `vue.config.js` 中修改：

- **开发环境**: 默认端口 8080，支持热重载
- **生产环境**: 代码压缩、Gzip 压缩、资源优化

### 代理配置
开发环境下配置了多个代理：
- `/api` - 主要业务接口代理
- `/wx-api` - 微信 API 代理
- `/mp-api` - 小程序 API 代理

## 部署说明

### Nginx 配置示例

```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /path/to/dist;
    index index.html;

    location / {
        try_files $uri $uri/ /index.html;
    }

    location /api {
        proxy_pass http://your-backend-server;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## 开发规范

### 代码规范
- 使用 ESLint 进行代码检查
- 遵循 Vue.js 官方风格指南
- 组件命名采用 PascalCase
- 文件命名采用 kebab-case

### Git 提交规范
项目集成了 Husky 和 lint-staged，在提交前会自动进行代码检查和格式化。

## 浏览器支持

- Chrome >= 60
- Firefox >= 60
- Safari >= 12
- Edge >= 79

## 许可证

[MIT License](LICENSE)

## 作者

詹Sir

## 贡献

欢迎提交 Issue 和 Pull Request 来改进项目。

## 更新日志

### v2.0.0
- 全新的 UI 设计
- 集成工作流引擎
- 增强的权限管理
- 优化的性能表现
