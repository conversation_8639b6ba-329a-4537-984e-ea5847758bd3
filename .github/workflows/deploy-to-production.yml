# 文件路径:.github/workflows/deploy-to-production.yml

# 工作流的名称，将显示在GitHub的Actions标签页中
name: Deploy to Production

# 触发条件：当有代码推送到'main'分支时触发
on:
  push:
    branches:
      - main

# 定义工作流中的作业
jobs:
  # 作业ID，可自定义
  build-and-deploy:
    # 指定作业运行的Runner环境
    runs-on: ubuntu-latest

    # 作业中包含的步骤，将按顺序执行
    steps:
      # 步骤1：检出代码
      # 使用官方的actions/checkout@v4，将仓库代码下载到Runner中
      - name: Checkout Code
        uses: actions/checkout@v4

      # 步骤2：设置Node.js环境
      # 使用官方的actions/setup-node@v4，安装指定版本的Node.js
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '16' # 指定Node.js版本，根据项目需求调整
          cache: 'npm'       # 启用对npm依赖的缓存，加速后续构建

      # 步骤3：安装项目依赖
      # 'npm ci'命令用于CI环境，它会根据package-lock.json进行确定性安装
      - name: Install Dependencies
        run: npm ci

      # 步骤4：运行代码检查（ESLint）
      - name: Run Linting
        run: |
          if npm run | grep -qE '^\s*lint\s'; then
            npm run lint
          else
            echo "No lint script defined, skipping."
          fi

      # 步骤5：运行测试（如果存在 test 脚本则运行，否则跳过）
      - name: Run Tests (if present)
        run: |
          if npm run | grep -qE '^\s*test\s'; then
            npm test
          else
            echo "No test script defined, skipping."
          fi

      # 步骤6：构建生产应用
      # 此步骤根据项目需求而定，包括编译Vue.js应用、打包前端资源等
      - name: Build Application
        run: npm run build
        env:
          NODE_ENV: production

      # 步骤7：在CI中构建并通过SCP上传产物到服务器
      - name: Prepare target directory on server
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.SSH_HOST }}
          username: ${{ secrets.SSH_USER }}
          port: ${{ secrets.SSH_PORT }}
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          script: |
            set -euo pipefail
            APP_DIR="/www/zn/zn.mp"
            mkdir -p "$APP_DIR"
            rm -rf "$APP_DIR"/*

      - name: Upload build artifacts via SCP
        uses: appleboy/scp-action@master
        with:
          host: ${{ secrets.SSH_HOST }}
          username: ${{ secrets.SSH_USER }}
          port: ${{ secrets.SSH_PORT }}
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          source: "dist/**"
          target: "/www/zn/zn.mp"
          overwrite: true

      - name: Finalize on server (permissions & reload nginx)
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.SSH_HOST }}
          username: ${{ secrets.SSH_USER }}
          port: ${{ secrets.SSH_PORT }}
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          script: |
            set -euo pipefail
            APP_DIR="/www/zn/zn.mp"
            chown nginx:nginx -R "$APP_DIR"
            chmod 755 -R "$APP_DIR"
            if command -v nginx >/dev/null 2>&1; then
              nginx -t
              nginx -s reload
            else
              echo "nginx not found, skip reload."
            fi

      # 步骤8：通过SSH部署到生产服务器（已弃用的服务器端构建步骤）
      # 使用社区广受欢迎的appleboy/ssh-action
      # 它会使用我们之前配置的Secrets安全地连接到服务器
      - name: "DEPRECATED: Old server-side build step (no-op)"
        if: ${{ false }}
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.SSH_HOST }}
          username: ${{ secrets.SSH_USER }}
          port: ${{ secrets.SSH_PORT }}
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          # 在服务器上执行的脚本
          script: |
            # 以下是在生产服务器上执行的命令，请根据您的实际部署流程进行定制
            # 这是一个使用PM2管理Node.js应用的示例

            # 1. 进入项目目录
            cd /www/zn/zn.mp

            # 2. 拉取最新的代码
            echo "Deprecated deploy step: skip git pull (using SCP-based deploy)."

            # 3. 安装生产环境依赖
            echo "skip npm install on server"

            # 4. 如果需要在服务器端构建，则执行构建命令
            echo "skip build on server"

            # 5. 重启应用
            echo "skip chown on deprecated step"
            echo "skip chmod on deprecated step"
            echo "skip nginx reload on deprecated step"
